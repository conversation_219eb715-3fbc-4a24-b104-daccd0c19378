const moment = require('moment');

// Test the date generation logic
function generateFutureDates(startDate, recurrencePattern, duration) {
  console.log('DEBUG generateFutureDates - Input:', {
    startDate,
    recurrencePattern,
    duration
  });

  // If recurrencePattern is an array of dates, use it directly
  if (Array.isArray(recurrencePattern)) {
    const result = recurrencePattern.map(date => {
      const fromDate = new Date(date);
      const toDate = new Date(fromDate);
      toDate.setMinutes(toDate.getMinutes() + duration);
      return { fromDate, toDate };
    });
    console.log('DEBUG generateFutureDates - Array result:', result);
    return result;
  }

  // Otherwise, generate dates based on the pattern
  const dates = [];
  const fromDate = new Date(startDate);
  const toDate = new Date(fromDate);
  toDate.setMinutes(toDate.getMinutes() + duration);

  console.log('DEBUG generateFutureDates - Base dates:', {
    fromDate: fromDate.toISOString(),
    toDate: toDate.toISOString()
  });

  // Add the first date
  dates.push({ fromDate: new Date(fromDate), toDate: new Date(toDate) });

  // If recurrencePattern is a string
  if (typeof recurrencePattern === 'string') {
    // Handle different recurrence patterns
    if (recurrencePattern.includes('Does Not Repeat')) {
      // Only one date for non-recurring events
      return dates;
    }

    // For recurring events, generate future dates
    const count = 12; // Generate 12 occurrences by default (about 3 months for weekly)

    // Determine interval based on pattern
    let interval = 7; // Default to weekly
    let dayOfWeek = -1; // Default to same day of week

    if (recurrencePattern.includes('Every Day')) {
      interval = 1; // Daily
    } else if (recurrencePattern.includes('Two Weeks')) {
      interval = 14; // Bi-weekly
    } else {
      // Check for specific day of week
      const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
      for (let i = 0; i < days.length; i++) {
        if (recurrencePattern.includes(days[i])) {
          dayOfWeek = i + 1; // 1 = Monday, 7 = Sunday
          break;
        }
      }
    }

    console.log('DEBUG - Pattern analysis:', { interval, dayOfWeek });

    // Generate dates using moment for more reliable date arithmetic
    for (let i = 1; i < count; i++) {
      let nextFromMoment = moment(fromDate);

      if (dayOfWeek > 0) {
        // If specific day of week is specified, find the next occurrence of that day
        nextFromMoment.add(i, 'weeks'); // Move forward by weeks
        
        // Adjust to the correct day of week if needed
        const targetDay = dayOfWeek === 7 ? 0 : dayOfWeek; // Convert 7 (Sunday) back to 0
        const currentDay = nextFromMoment.day();
        if (currentDay !== targetDay) {
          const daysToAdd = (targetDay - currentDay + 7) % 7;
          nextFromMoment.add(daysToAdd, 'days');
        }
      } else {
        // Otherwise just add the interval in days
        nextFromMoment.add(i * interval, 'days');
      }

      const nextToMoment = nextFromMoment.clone().add(duration, 'minutes');

      console.log(`DEBUG generateFutureDates - Generated date ${i}:`, {
        nextFromDate: nextFromMoment.toISOString(),
        nextToDate: nextToMoment.toISOString()
      });

      dates.push({ 
        fromDate: nextFromMoment.toDate(), 
        toDate: nextToMoment.toDate() 
      });
    }
  }

  console.log('DEBUG generateFutureDates - Final result:', dates.map(d => ({
    fromDate: d.fromDate.toISOString(),
    toDate: d.toDate.toISOString()
  })));

  return dates;
}

// Test with the scenario from the screenshot
console.log('=== Testing with 09/07/2025 11:00 ===');
const testDate = '2025-07-09T11:00:00.000Z';
const testPattern = 'Every Week';
const testDuration = 60;

const result = generateFutureDates(testDate, testPattern, testDuration);

console.log('\n=== Summary ===');
console.log('Generated dates:');
result.forEach((date, index) => {
  console.log(`${index + 1}. ${moment(date.fromDate).format('YYYY-MM-DD HH:mm')} - ${moment(date.toDate).format('HH:mm')}`);
});
